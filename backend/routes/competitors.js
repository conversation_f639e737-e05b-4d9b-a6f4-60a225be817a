import express from "express";
import PluginCompetitor from "../models/PluginCompetitor.js";
import PluginKeyword from "../models/PluginKeyword.js";
import Plugin from "../models/Plugin.js";
import { authenticateToken } from "../middleware/auth.js";

const router = express.Router();

// Helper function to get current rank from plugins collection
const getCurrentRank = async (pluginSlug) => {
  try {
    const plugin = await Plugin.findBySlug(pluginSlug);
    return plugin ? plugin.currentRank : null;
  } catch (error) {
    console.error(`Error getting rank for ${pluginSlug}:`, error);
    return null;
  }
};

// Helper function to fetch plugin data from WordPress API
const fetchPluginData = async (pluginSlug) => {
  try {
    const apiUrl = `https://api.wordpress.org/plugins/info/1.2/?action=plugin_information&request[slug]=${encodeURIComponent(
      pluginSlug
    )}&request[fields][icons]=true`;

    const response = await fetch(apiUrl);
    if (!response.ok) {
      throw new Error(`WordPress API request failed: ${response.status}`);
    }

    const data = await response.json();

    if (data.error) {
      throw new Error(data.error);
    }

    return {
      pluginName: data.name,
      activeInstalls: data.active_installs || 0,
      version: data.version,
      rating: data.rating,
      numRatings: data.num_ratings,
      author: data.author,
      homepage: data.homepage,
      shortDescription: data.short_description,
      tags: data.tags || [],
    };
  } catch (error) {
    console.error(`Error fetching plugin data for ${pluginSlug}:`, error);
    return null;
  }
};

// Helper function to discover competitors from keywords
const discoverCompetitorsFromKeywords = async (userId) => {
  try {
    // Get all user keywords
    const userKeywords = await PluginKeyword.getUserKeywords(userId);

    if (userKeywords.length === 0) {
      return [];
    }

    const discoveredCompetitors = [];

    // For each keyword, search WordPress API for plugins
    for (const keywordDoc of userKeywords) {
      try {
        const searchUrl = `https://api.wordpress.org/plugins/info/1.2/?action=query_plugins&request[search]=${encodeURIComponent(
          keywordDoc.keyword
        )}&request[per_page]=20`;

        const response = await fetch(searchUrl);
        if (!response.ok) continue;

        const data = await response.json();

        if (data.plugins && Array.isArray(data.plugins)) {
          // Filter plugins with more than 1000 active installs and sort by active installs
          const relevantPlugins = data.plugins
            .filter((plugin) => plugin.active_installs > 1000)
            .sort((a, b) => b.active_installs - a.active_installs)
            .slice(0, 10); // Take top 10

          for (const plugin of relevantPlugins) {
            // Skip if this plugin is already in user's added plugins
            const isUserPlugin = userKeywords.some(
              (uk) => uk.pluginSlug === plugin.slug
            );
            if (isUserPlugin) continue;

            // Get current rank from plugins collection
            const currentRank = await getCurrentRank(plugin.slug);

            discoveredCompetitors.push({
              pluginSlug: plugin.slug,
              pluginName: plugin.name,
              activeInstalls: plugin.active_installs,
              currentRank: currentRank,
              keywords: [keywordDoc.keyword],
              discoveredFromKeywords: [keywordDoc.keyword],
              version: plugin.version,
              rating: plugin.rating,
              numRatings: plugin.num_ratings,
              author: plugin.author,
              homepage: plugin.homepage,
              shortDescription: plugin.short_description,
              tags: plugin.tags || [],
            });
          }
        }
      } catch (error) {
        console.error(
          `Error searching for keyword ${keywordDoc.keyword}:`,
          error
        );
        continue;
      }
    }

    // Merge competitors with same slug
    const mergedCompetitors = {};
    discoveredCompetitors.forEach((competitor) => {
      if (mergedCompetitors[competitor.pluginSlug]) {
        // Merge keywords
        mergedCompetitors[competitor.pluginSlug].keywords = [
          ...new Set([
            ...mergedCompetitors[competitor.pluginSlug].keywords,
            ...competitor.keywords,
          ]),
        ];
        mergedCompetitors[competitor.pluginSlug].discoveredFromKeywords = [
          ...new Set([
            ...mergedCompetitors[competitor.pluginSlug].discoveredFromKeywords,
            ...competitor.discoveredFromKeywords,
          ]),
        ];
      } else {
        mergedCompetitors[competitor.pluginSlug] = competitor;
      }
    });

    return Object.values(mergedCompetitors);
  } catch (error) {
    console.error("Error discovering competitors:", error);
    return [];
  }
};

// Get competitors for user
router.get("/", authenticateToken, async (req, res) => {
  try {
    const userId = req.user._id;
    const { autoDiscover = false } = req.query;

    let competitors = await PluginCompetitor.getUserCompetitors(userId);

    // Auto-discover competitors if requested and no competitors exist
    if (autoDiscover && competitors.length === 0) {
      const discoveredCompetitors = await discoverCompetitorsFromKeywords(
        userId
      );

      // Save discovered competitors to database
      for (const competitorData of discoveredCompetitors) {
        try {
          await PluginCompetitor.addCompetitorForUser(userId, {
            ...competitorData,
            isManuallyAdded: false,
          });
        } catch (error) {
          console.error(
            `Error saving competitor ${competitorData.pluginSlug}:`,
            error
          );
        }
      }

      // Reload competitors after discovery
      competitors = await PluginCompetitor.getUserCompetitors(userId);
    }

    res.json({
      success: true,
      competitors,
      count: competitors.length,
    });
  } catch (error) {
    console.error("Get competitors error:", error);
    res.status(500).json({
      success: false,
      message: "Failed to get competitors",
      error: error.message,
    });
  }
});

// Add new competitor manually
router.post("/", authenticateToken, async (req, res) => {
  try {
    const userId = req.user._id;
    const { pluginSlug } = req.body;

    if (!pluginSlug) {
      return res.status(400).json({
        success: false,
        message: "Plugin slug is required",
      });
    }

    // Fetch plugin data from WordPress API
    const pluginData = await fetchPluginData(pluginSlug);

    if (!pluginData) {
      return res.status(404).json({
        success: false,
        message: "Plugin not found or failed to fetch plugin data",
      });
    }

    // Get current rank from plugins collection
    const currentRank = await getCurrentRank(pluginSlug);

    const newCompetitor = await PluginCompetitor.addCompetitorForUser(userId, {
      pluginSlug,
      pluginName: pluginData.pluginName,
      activeInstalls: pluginData.activeInstalls,
      currentRank: currentRank,
      version: pluginData.version,
      rating: pluginData.rating,
      numRatings: pluginData.numRatings,
      author: pluginData.author,
      homepage: pluginData.homepage,
      shortDescription: pluginData.shortDescription,
      tags: pluginData.tags,
      isManuallyAdded: true,
    });

    res.status(201).json({
      success: true,
      message: "Competitor added successfully",
      competitor: newCompetitor,
    });
  } catch (error) {
    console.error("Add competitor error:", error);

    if (error.message.includes("already exists")) {
      return res.status(409).json({
        success: false,
        message: "Competitor already exists",
      });
    }

    res.status(500).json({
      success: false,
      message: "Failed to add competitor",
      error: error.message,
    });
  }
});

// Auto-discover competitors based on keywords
router.post("/discover", authenticateToken, async (req, res) => {
  try {
    const userId = req.user._id;

    const discoveredCompetitors = await discoverCompetitorsFromKeywords(userId);

    let savedCount = 0;
    const errors = [];

    // Save discovered competitors to database
    for (const competitorData of discoveredCompetitors) {
      try {
        await PluginCompetitor.addCompetitorForUser(userId, {
          ...competitorData,
          isManuallyAdded: false,
        });
        savedCount++;
      } catch (error) {
        errors.push({
          pluginSlug: competitorData.pluginSlug,
          error: error.message,
        });
      }
    }

    res.json({
      success: true,
      message: `Discovered and saved ${savedCount} competitors`,
      savedCount,
      totalDiscovered: discoveredCompetitors.length,
      errors: errors.length > 0 ? errors : undefined,
    });
  } catch (error) {
    console.error("Discover competitors error:", error);
    res.status(500).json({
      success: false,
      message: "Failed to discover competitors",
      error: error.message,
    });
  }
});

// Delete competitor
router.delete("/:competitorId", authenticateToken, async (req, res) => {
  try {
    const userId = req.user._id;
    const { competitorId } = req.params;

    const competitor = await PluginCompetitor.findOneAndUpdate(
      { _id: competitorId, userId },
      { isActive: false },
      { new: true }
    );

    if (!competitor) {
      return res.status(404).json({
        success: false,
        message: "Competitor not found",
      });
    }

    res.json({
      success: true,
      message: "Competitor deleted successfully",
    });
  } catch (error) {
    console.error("Delete competitor error:", error);
    res.status(500).json({
      success: false,
      message: "Failed to delete competitor",
      error: error.message,
    });
  }
});

// Update competitor data
router.put("/:competitorId", authenticateToken, async (req, res) => {
  try {
    const userId = req.user._id;
    const { competitorId } = req.params;
    const updateData = req.body;

    const updatedCompetitor = await PluginCompetitor.findOneAndUpdate(
      { _id: competitorId, userId },
      {
        ...updateData,
        lastUpdated: new Date(),
      },
      { new: true }
    );

    if (!updatedCompetitor) {
      return res.status(404).json({
        success: false,
        message: "Competitor not found",
      });
    }

    res.json({
      success: true,
      message: "Competitor updated successfully",
      competitor: updatedCompetitor,
    });
  } catch (error) {
    console.error("Update competitor error:", error);
    res.status(500).json({
      success: false,
      message: "Failed to update competitor",
      error: error.message,
    });
  }
});

export default router;
