import { useState, useEffect } from "react";
import {
  Plus,
  Download,
  Trash2,
  RefreshCw,
  Package,
  BarChart,
} from "lucide-react";
import Modal from "../components/Modal";
import PluginAnalyticsModal from "../components/PluginAnalyticsModal";
import { useAuth } from "../contexts/AuthContext";
import { getValidToken, handleAuthError } from "../utils/tokenUtils";
import { makeAuthenticatedRequest } from "../utils/apiConfig";

// Plugin Card Component
const PluginCard = ({ plugin, onRemove, onRefresh, canAddPlugins }) => {
  const [showAnalytics, setShowAnalytics] = useState(false);
  const [isRefreshing, setIsRefreshing] = useState(false);

  // Use enhanced data from backend instead of separate API calls

  // Format date to dd-mm-yyyy and calculate days difference
  // Example: formatDateAndDiff("2025-06-24 7:02am GMT") returns { formatted: "24-06-2025", daysDiff: 9 }
  const formatDateAndDiff = (dateString) => {
    if (!dateString) return { formatted: "N/A", daysDiff: "N/A" };

    try {
      // console.log("first", dateString);

      // Extract YYYY-MM-DD from the string
      const match = dateString.match(/^(\d{4})-(\d{2})-(\d{2})/);
      if (!match) return { formatted: "N/A", daysDiff: "N/A" };

      const [, year, month, day] = match;

      // Format to dd-mm-yyyy
      const formatted = `${day}-${month}-${year}`;

      // Parse date safely
      const fromDate = new Date(`${year}-${month}-${day}`);
      const toDate = new Date();

      if (isNaN(fromDate.getTime()))
        return { formatted: "N/A", daysDiff: "N/A" };

      // Calculate day difference
      const diffTime = toDate - fromDate;
      // const daysDiff = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
      const daysDiff = Math.floor(diffTime / (1000 * 60 * 60 * 24));

      // console.log("Formatted:", formatted);
      // console.log("Days difference:", daysDiff);

      return { formatted, daysDiff };
    } catch (error) {
      return { formatted: "N/A", daysDiff: "N/A" };
    }
  };

  // Data is now provided directly from the backend API

  // Handle refresh plugin
  const handleRefresh = async () => {
    if (!onRefresh) return;
    setIsRefreshing(true);
    try {
      await onRefresh(plugin.slug);
      // Data will be refreshed when parent component reloads added plugins
    } finally {
      setIsRefreshing(false);
    }
  };

  return (
    <>
      <div className="bg-white border border-gray-200 rounded-lg p-6 hover:shadow-lg transition-all duration-200">
        {/* Header */}
        <div className="flex items-start justify-between mb-4">
          <div className="flex items-center space-x-2 flex-1 overflow-hidden">
            {/* <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center overflow-hidden"> */}
            <div className="w-12 h-12 border rounded-lg flex items-center justify-center overflow-hidden">
              {plugin.icons && (plugin.icons["2x"] || plugin.icons["1x"]) ? (
                <img
                  src={plugin.icons["2x"] || plugin.icons["1x"]}
                  alt={`${plugin.displayName} icon`}
                  className="w-full h-full object-cover rounded-lg"
                  onError={(e) => {
                    // Fallback to Package icon if image fails to load
                    e.target.style.display = "none";
                    e.target.nextSibling.style.display = "flex";
                  }}
                />
              ) : null}
              <Package
                className={`h-6 w-6 text-black ${
                  plugin.icons && (plugin.icons["2x"] || plugin.icons["1x"])
                    ? "hidden"
                    : ""
                }`}
              />
            </div>
            <div className="flex-1 min-w-0">
              <h4 className="font-semibold text-gray-900 truncate text-lg">
                {plugin.displayName}
              </h4>
              <p className="text-sm text-gray-500 font-mono whitespace-nowrap">
                {plugin.slug}
              </p>
            </div>
          </div>
          <div className="flex items-center space-x-1">
            {/* {canAddPlugins && ( */}
            <button
              onClick={handleRefresh}
              disabled={isRefreshing}
              className="text-gray-400 hover:text-green-500 transition-colors p-1 disabled:opacity-50"
              title="Refresh plugin data"
            >
              <RefreshCw
                className={`h-4 w-4 ${isRefreshing ? "animate-spin" : ""}`}
              />
            </button>
            {/* )} */}
            {canAddPlugins && (
              <button
                onClick={() => onRemove(plugin.slug)}
                className="text-gray-400 hover:text-red-500 transition-colors p-1"
                title="Remove plugin"
              >
                <Trash2 className="h-4 w-4" />
              </button>
            )}
          </div>
        </div>

        {/* Plugin Info */}
        <div className="space-y-3 mb-4">
          {/* Version and Rank in single row */}
          <div className="flex items-center justify-between space-x-4">
            <span className="text-sm font-medium px-2 py-1 rounded-full bg-green-100 text-green-800">
              v{plugin.version || "N/A"}
            </span>
            <div className="flex items-center space-x-1">
              <span className="text-sm font-medium text-gray-900">
                #{plugin.currentRank || "N/A"}
              </span>
              {plugin.rankHistory?.rankChange !== null &&
                plugin.rankHistory?.rankChange !== undefined && (
                  <span
                    className={`text-xs ${
                      plugin.rankHistory.rankChange > 0
                        ? "text-green-600"
                        : plugin.rankHistory.rankChange < 0
                        ? "text-red-600"
                        : "text-gray-600"
                    }`}
                  >
                    {plugin.rankHistory.rankChange > 0
                      ? "↑"
                      : plugin.rankHistory.rankChange < 0
                      ? "↓"
                      : "→"}
                    {Math.abs(plugin.rankHistory.rankChange)}
                  </span>
                )}
            </div>
          </div>

          {/* Plugin Rating */}
          {/* <div className="flex items-center justify-between">
            <span className="text-sm text-gray-600">Rating</span>
            <div className="flex items-center space-x-2">
              {plugin.rating ? (
                <>
                  <div className="flex items-center">
                    <span className="text-sm font-medium text-yellow-600">
                      ★ {(plugin.rating / 20).toFixed(1)}
                    </span>
                    <span className="text-xs text-gray-500 ml-1">
                      ({plugin.numRatings || 0} reviews)
                    </span>
                  </div>
                </>
              ) : (
                <span className="text-sm text-gray-500">No ratings</span>
              )}
            </div>
          </div> */}

          {/* Released date with color coding */}
          <div className="flex items-center justify-between">
            <span className="text-sm text-gray-600">Released</span>
            <div
              className={`text-sm font-medium px-2 py-1 rounded ${(() => {
                // Use lastReleaseDate from plugininformations collection
                const dateToUse = plugin.lastReleaseDate || plugin.lastFetched;
                const result = formatDateAndDiff(dateToUse);

                if (result.daysDiff === "N/A" || result.daysDiff <= 20) {
                  return "bg-gray-100 text-gray-700";
                } else {
                  return "bg-yellow-50 text-yellow-700";
                }
              })()}`}
            >
              {(() => {
                // Use lastReleaseDate from plugininformations collection
                const dateToUse = plugin.lastReleaseDate || plugin.lastFetched;

                const result = formatDateAndDiff(dateToUse);

                return (
                  <>
                    {result.formatted}
                    {result.daysDiff !== "N/A" && (
                      <span className="text-xs ml-1">
                        ({result.daysDiff} days)
                      </span>
                    )}
                  </>
                );
              })()}
            </div>
          </div>

          {/* Previous Versions */}
          {/* {plugin.previousVersions && plugin.previousVersions.length > 0 && (
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">Previous Versions</span>
              <div className="flex flex-wrap gap-1">
                {plugin.previousVersions.slice(0, 3).map((version, index) => (
                  <span
                    key={index}
                    className="text-xs px-1 py-0.5 bg-gray-100 text-gray-600 rounded"
                  >
                    v{version}
                  </span>
                ))}
                {plugin.previousVersions.length > 3 && (
                  <span className="text-xs text-gray-500">
                    +{plugin.previousVersions.length - 3} more
                  </span>
                )}
              </div>
            </div>
          )} */}
        </div>

        {/* Download Trends Table */}
        <div className="mt-4 border border-gray-200 rounded-lg overflow-hidden">
          {/* Table Header */}
          <div className="bg-gray-50 px-3 py-2 border-b border-gray-200">
            <div className="flex items-center justify-between">
              <h5 className="text-sm font-medium text-gray-900">
                Download Trends
              </h5>
              {plugin.downloadTrend && (
                <span
                  className={`text-xs px-2 py-1 rounded-full ${
                    plugin.downloadTrend.isPositive
                      ? "bg-green-100 text-green-800"
                      : "bg-red-100 text-red-800"
                  }`}
                >
                  {plugin.downloadTrend.isPositive ? "↑" : "↓"}{" "}
                  {plugin.downloadTrend.changePercent}%
                </span>
              )}
            </div>
          </div>

          {/* Download Trends Summary */}
          {/* {plugin.downloadTrend && (
            <div className="bg-blue-50 px-3 py-2 border-b border-gray-200">
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="text-blue-700">Yesterday:</span>
                  <span className="ml-2 font-medium text-blue-900">
                    {plugin.downloadTrend.yesterdayDownloads.toLocaleString()}
                  </span>
                </div>
                <div>
                  <span className="text-blue-700">Day Before:</span>
                  <span className="ml-2 font-medium text-blue-900">
                    {plugin.downloadTrend.dayBeforeDownloads.toLocaleString()}
                  </span>
                </div>
              </div>
            </div>
          )} */}

          {/* Table Body */}
          {plugin.downloadTrend ? (
            <table className="min-w-full divide-y divide-gray-200">
              {/* <thead className="bg-gray-50">
                <tr>
                  <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Date
                  </th>
                  <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Downloads
                  </th>
                </tr>
              </thead> */}
              <tbody className="bg-white divide-y divide-gray-200">
                {/* Yesterday Row */}
                <tr className="">
                  <td className="px-4 py-2 whitespace-nowrap text-sm font-medium text-gray-700">
                    Yesterday
                    {/* (
                    {(() => {
                      const yesterday = new Date();
                      yesterday.setDate(yesterday.getDate() - 1);
                      return yesterday.toLocaleDateString("en-GB");
                    })()}
                    ) */}
                  </td>
                  <td className="px-4 py-2 whitespace-nowrap text-sm text-gray-700 font-bold text-right">
                    {plugin.downloadTrend.yesterdayDownloads.toLocaleString()}
                  </td>
                </tr>
                {/* Day Before Yesterday Row */}
                <tr className="">
                  <td className="px-4 py-2 whitespace-nowrap text-sm font-medium text-gray-700">
                    Day Before
                    {/* (
                    {(() => {
                      const dayBefore = new Date();
                      dayBefore.setDate(dayBefore.getDate() - 2);
                      return dayBefore.toLocaleDateString("en-GB");
                    })()}
                    ) */}
                  </td>
                  <td className="px-4 py-2 whitespace-nowrap text-sm text-gray-900 font-bold text-right">
                    {plugin.downloadTrend.dayBeforeDownloads.toLocaleString()}
                  </td>
                </tr>
              </tbody>
              {/* Table Footer with Total Changes */}
              <tfoot className="bg-gray-100 border-t border-gray-200">
                <tr>
                  <td className="px-4 py-2 text-sm font-semibold text-gray-900">
                    Changes
                  </td>
                  <td className="px-4 py-2 whitespace-nowrap text-right">
                    <span
                      className={`text-sm font-bold ${
                        plugin.downloadTrend.change >= 0
                          ? "text-green-600"
                          : "text-red-600"
                      }`}
                    >
                      {plugin.downloadTrend.change >= 0 ? "+" : ""}
                      {plugin.downloadTrend.change.toLocaleString()}
                    </span>
                    {/* <span className="text-xs text-gray-500 ml-2">
                      ({plugin.downloadTrend.change >= 0 ? "↑" : "↓"}{" "}
                      {plugin.downloadTrend.changePercent}%)
                    </span> */}
                  </td>
                </tr>
              </tfoot>
            </table>
          ) : (
            <div className="text-center py-4">
              <span className="text-xs text-gray-500">
                No download trend data available
              </span>
            </div>
          )}
        </div>

        {/* Footer Button */}
        <div className="pt-4 border-t border-gray-100">
          <button
            onClick={() => setShowAnalytics(true)}
            className="w-full bg-gradient-to-r from-blue-600 to-purple-600 text-white py-2 px-4 rounded-lg font-medium hover:from-blue-700 hover:to-purple-700 transition-all duration-200 flex items-center justify-center space-x-2"
          >
            <BarChart className="h-4 w-4" />
            <span>View Chart & Analytics</span>
          </button>
        </div>
      </div>

      {/* Analytics Modal */}
      <PluginAnalyticsModal
        isOpen={showAnalytics}
        onClose={() => setShowAnalytics(false)}
        plugin={plugin}
      />
    </>
  );
};

const Dashboard = () => {
  const { user, autoLogout } = useAuth();
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [pluginSlug, setPluginSlug] = useState("");
  const [isFetching, setIsFetching] = useState(false);
  const [hasPluginsInDB, setHasPluginsInDB] = useState(false);
  const [fetchProgress, setFetchProgress] = useState(null);
  const [addedPlugins, setAddedPlugins] = useState([]);

  // console.log("addedPlugins are:", addedPlugins);

  // New states for fetch functionality
  const [fetchedPluginData, setFetchedPluginData] = useState(null);
  const [fetchingPluginData, setFetchingPluginData] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [pluginToDelete, setPluginToDelete] = useState(null);
  const [isAddingPlugin, setIsAddingPlugin] = useState(false);
  const [isRefreshingAll, setIsRefreshingAll] = useState(false);
  const [refreshProgress, setRefreshProgress] = useState(null);

  // console.log("Fetched Plugin Data:", fetchedPluginData);

  // Check if user can add plugins (admin or superadmin)
  const canAddPlugins = user && ["admin", "superadmin"].includes(user.role);

  // Check database status for button text
  const checkDatabaseStatus = async () => {
    try {
      const token = getValidToken();
      if (!token) {
        console.warn("No valid authentication token found for database check");
        autoLogout("No valid authentication token found. Please login again.");
        return;
      }

      const BASE_URL =
        import.meta.env.VITE_API_BASE_URL || "http://localhost:5001";
      const response = await fetch(`${BASE_URL}/api/plugins/check-database`, {
        headers: {
          Authorization: `Bearer ${token}`,
          "Content-Type": "application/json",
        },
      });

      if (response.ok) {
        const data = await response.json();
        if (data.success) {
          setHasPluginsInDB(data.hasPlugins);
        }
      } else if (response.status === 401) {
        console.warn("Authentication failed during database check");

        // Try to get the specific error message from the response
        let errorMessage = "Your session has expired. Please login again.";
        try {
          const errorData = await response.json();
          if (errorData.message) {
            errorMessage = errorData.message;
          }
        } catch (e) {
          // If we can't parse the error response, use default message
        }

        autoLogout(errorMessage);
      } else {
        handleAuthError(null, response);
      }
    } catch (error) {
      console.error("Error checking database status:", error);

      // Handle JWT token expiration errors
      if (
        error.message &&
        (error.message.includes("expired") ||
          error.message.includes("token") ||
          error.message.includes("Authentication failed"))
      ) {
        console.warn(
          "JWT token error detected during database check:",
          error.message
        );
        autoLogout(error.message);
        return;
      }

      handleAuthError(error);
    }
  };

  // Fetch ALL plugins (55,540+ plugins) using streaming response
  const handleFetchAllPlugins = async () => {
    try {
      setIsFetching(true);
      setFetchProgress({
        current: 0,
        total: 0,
        page: 0,
        totalPages: 0,
        successCount: 0,
        errorCount: 0,
        percentComplete: 0,
        estimatedTimeRemaining: null,
        averageTimePerPage: null,
        pluginsPerSecond: 0,
        message: "Starting full plugin fetch (all 55,540+ plugins)...",
      });

      const token = getValidToken();
      if (!token) {
        console.error("No valid authentication token found");
        autoLogout("No valid authentication token found. Please login again.");
        return;
      }

      const BASE_URL =
        import.meta.env.VITE_API_BASE_URL || "http://localhost:5001";

      // Use fetch-all endpoint with streaming response
      const response = await fetch(`${BASE_URL}/api/plugins/fetch-all`, {
        method: "POST",
        headers: {
          Authorization: `Bearer ${token}`,
          "Content-Type": "application/json",
        },
      });

      if (!response.ok) {
        if (response.status === 401) {
          console.warn("Authentication failed during plugin fetch");

          // Try to get the specific error message from the response
          let errorMessage = "Your session has expired. Please login again.";
          try {
            const errorData = await response.json();
            if (errorData.message) {
              errorMessage = errorData.message;
            }
          } catch (e) {
            // If we can't parse the error response, use default message
          }

          autoLogout(errorMessage);
          return;
        }
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      // Handle streaming response
      const reader = response.body.getReader();
      const decoder = new TextDecoder();

      while (true) {
        const { done, value } = await reader.read();
        if (done) break;

        const chunk = decoder.decode(value);
        const lines = chunk.split("\n").filter((line) => line.trim());

        for (const line of lines) {
          try {
            const data = JSON.parse(line);

            if (data.type === "progress") {
              setFetchProgress({
                current: data.current || 0,
                total: data.total || 0,
                page: data.page || 0,
                totalPages: data.totalPages || 0,
                successCount: data.successCount || 0,
                errorCount: data.errorCount || 0,
                percentComplete: data.percentComplete || 0,
                estimatedTimeRemaining: data.estimatedTimeRemaining,
                averageTimePerPage: data.averageTimePerPage,
                pluginsPerSecond: data.pluginsPerSecond || 0,
                message: data.message || "Processing...",
              });
            } else if (data.type === "complete") {
              setFetchProgress({
                current: data.summary.totalProcessedPlugins,
                total: data.summary.totalPlugins,
                page: data.summary.totalPages,
                totalPages: data.summary.totalPages,
                successCount: data.summary.successfulPages,
                errorCount: data.summary.failedPages,
                percentComplete: 100,
                averageTimePerPage: data.summary.averageTimePerPage,
                pluginsPerSecond: data.summary.averagePluginsPerSecond,
                successRate: data.summary.successRate,
                totalDuration: data.summary.totalDuration,
                message: `✅ Fetch completed! ${data.summary.totalProcessedPlugins.toLocaleString()} plugins processed in ${Math.round(
                  data.summary.totalDuration / 1000 / 60
                )} minutes`,
              });

              // console.log("✅ Full fetch completed successfully!");
              // console.log("Summary:", data.summary);

              if (data.errors && data.errors.length > 0) {
                console.warn("Some errors occurred during fetch:", data.errors);
              }

              window.toast(
                `Successfully fetched ${data.summary.totalProcessedPlugins.toLocaleString()} plugins!`,
                "success"
              );
            } else if (data.type === "error") {
              throw new Error(data.message || "Fetch failed");
            }
          } catch (parseError) {
            console.warn("Failed to parse streaming data:", parseError);
          }
        }
      }
    } catch (error) {
      console.error("Fetch all plugins error:", error);

      // Handle JWT token expiration errors
      if (
        error.message &&
        (error.message.includes("expired") ||
          error.message.includes("token") ||
          error.message.includes("Authentication failed"))
      ) {
        console.warn(
          "JWT token error detected during plugin fetch:",
          error.message
        );
        autoLogout(error.message);
        return;
      }

      setFetchProgress({
        current: 0,
        total: 0,
        message: `❌ Full fetch failed: ${error.message}`,
        error: true,
      });

      window.toast(`Fetch failed: ${error.message}`, "error");
    } finally {
      setIsFetching(false);
      setTimeout(() => setFetchProgress(null), 10000); // Show results for 10 seconds
      // Refresh database status after fetch
      checkDatabaseStatus();
    }
  };

  // Load added plugins from database
  const loadAddedPlugins = async () => {
    try {
      // console.log("Loading added plugins...");

      // Use the makeAuthenticatedRequest utility which handles JWT token expiration automatically
      const response = await makeAuthenticatedRequest("/api/plugins/added");

      if (!response.ok) {
        // For other HTTP errors, try to get the error message
        let errorMessage = `HTTP error! status: ${response.status}`;
        try {
          const errorData = await response.json();
          if (errorData.message) {
            errorMessage = errorData.message;
          }
        } catch (e) {
          // If we can't parse the error response, use default message
        }

        throw new Error(errorMessage);
      }

      const data = await response.json();
      // console.log("Plugin data: ", data);

      if (data.success) {
        // Transform the data to match the expected format with enhanced data
        const transformedPlugins = data.addedPlugins.map((plugin) => ({
          slug: plugin.pluginSlug,
          name: plugin.pluginName,
          displayName: plugin.displayName,
          // Use currentRank from the enhanced data
          currentRank: plugin.currentRank,
          rankGrowth: plugin.rankHistory?.rankChange || 0,
          lastFetched: plugin.lastUpdated,
          short_description: plugin.short_description,
          // Plugin version and release date from plugininformations collection
          version: plugin.version,
          lastReleaseDate: plugin.lastReleaseDate,
          // Plugin icons from addedplugins collection
          icons: plugin.icons || {},
          // Plugin ratings from plugininformations collection
          rating: plugin.rating,
          numRatings: plugin.numRatings || 0,
          // Plugin versions (current and previous) from plugininformations collection
          currentVersion: plugin.currentVersion,
          previousVersions: plugin.previousVersions || [],
          // Enhanced data from all collections
          rankHistory: plugin.rankHistory,
          downloadTrend: plugin.downloadTrend,
          downloadDataHistory: plugin.downloadDataHistory || [],
          reviewStats: plugin.reviewStats,
          versionInfo: plugin.versionInfo,
          pluginInformation: plugin.pluginInformation,
        }));
        setAddedPlugins(transformedPlugins);
      } else {
        console.warn("Failed to load added plugins:", data.message);
      }
    } catch (error) {
      console.error("Error loading added plugins:", error);

      // JWT token expiration is handled automatically by makeAuthenticatedRequest
      // through the auth-error event, so we don't need to handle it manually here

      if (
        error.name === "TypeError" &&
        error.message.includes("Failed to fetch")
      ) {
        console.warn("Unable to connect to server - backend may be down");
        // Don't crash the app, just log the error
        setAddedPlugins([]); // Set empty array as fallback
      } else {
        // For other errors, show a generic message but don't crash
        console.warn("Failed to load added plugins:", error.message);
        setAddedPlugins([]); // Set empty array as fallback
      }
    }
  };

  // LocalStorage utility functions
  const setPluginLocalStorage = (pluginData) => {
    // console.log("Setting plugin data in localStorage:", pluginData);
    try {
      localStorage.setItem("pluginData", JSON.stringify(pluginData));
      // console.log("Plugin data stored in localStorage successfully");
    } catch (error) {
      console.error("Error storing plugin data in localStorage:", error);
    }
  };

  const getPluginLocalStorage = () => {
    try {
      const storedData = localStorage.getItem("pluginData");
      // console.log("Retrieved from localStorage:", storedData);

      if (storedData) {
        const parsedData = JSON.parse(storedData);
        // console.log("Parsed localStorage data:", parsedData);
        return parsedData;
      }
      // console.log("No plugin data found in localStorage");
      return null;
    } catch (error) {
      console.error("Error retrieving plugin data from localStorage:", error);
      return null;
    }
  };

  const clearPluginLocalStorage = () => {
    // console.log("Clearing plugin data from localStorage");
    localStorage.removeItem("pluginData");
  };

  // Fetch plugin data from WordPress API and store in localStorage
  const handleFetchPluginData = async () => {
    if (!pluginSlug.trim()) {
      window.toast("Please enter a plugin slug", "warning");
      return;
    }

    try {
      setFetchingPluginData(true);

      // Fetch plugin information directly from WordPress API
      const wpApiUrl = `https://api.wordpress.org/plugins/info/1.2/?action=plugin_information&request[slug]=${pluginSlug.trim()}&request[fields][icons]=true`;

      // console.log("Fetching plugin data from WordPress API:", wpApiUrl);

      const response = await fetch(wpApiUrl);

      if (!response.ok) {
        throw new Error(`WordPress API error: ${response.status}`);
      }

      const pluginData = await response.json();

      if (pluginData.error) {
        window.toast(`Plugin not found: ${pluginData.error}`, "error");
        setFetchedPluginData(null);
        return;
      }

      // Filter plugin data to only include essential fields to avoid payload size issues
      const essentialPluginData = {
        slug: pluginData.slug,
        name: pluginData.name,
        version: pluginData.version,
        author: pluginData.author,
        rating: pluginData.rating,
        active_installs: pluginData.active_installs,
        num_ratings: pluginData.num_ratings,
        downloaded: pluginData.downloaded,
        last_updated: pluginData.last_updated,
        short_description: pluginData.short_description,
        homepage: pluginData.homepage,
        requires: pluginData.requires,
        tested: pluginData.tested,
        requires_php: pluginData.requires_php,
        // Include icons but limit to essential ones
        icons: pluginData.icons
          ? {
              "1x": pluginData.icons["1x"],
              "2x": pluginData.icons["2x"],
            }
          : {},
        // Include only a limited number of tags to avoid large payloads
        tags: pluginData.tags ? Object.keys(pluginData.tags).slice(0, 10) : [],
        // Exclude large fields like sections, screenshots, banners, versions to prevent payload size issues
      };

      // Store filtered plugin data in localStorage
      setPluginLocalStorage(essentialPluginData);

      // Set the fetched plugin data for display in modal
      setFetchedPluginData({
        slug: pluginData.slug,
        name: pluginData.name,
        version: pluginData.version,
        author: pluginData.author,
        rating: pluginData.rating,
        active_installs: pluginData.active_installs,
        num_ratings: pluginData.num_ratings,
        downloaded: pluginData.downloaded,
        last_updated: pluginData.last_updated,
        // short_description: pluginData.short_description,
        homepage: pluginData.homepage,
        requires: pluginData.requires,
        tested: pluginData.tested,
        requires_php: pluginData.requires_php,
      });

      window.toast(
        "Plugin data fetched successfully from WordPress API",
        "success"
      );
    } catch (error) {
      console.error("Error fetching plugin data:", error);
      window.toast("Failed to fetch plugin data from WordPress API", "error");
      setFetchedPluginData(null);
    } finally {
      setFetchingPluginData(false);
    }
  };

  // Load added plugins and check database status on component mount
  useEffect(() => {
    loadAddedPlugins();
    checkDatabaseStatus();
  }, []);

  const handleAddPlugin = async () => {
    // Check if plugin data is available in localStorage
    const pluginDataFromStorage = getPluginLocalStorage();

    // console.log("Plugin data from localStorage:", pluginDataFromStorage);
    // console.log("Fetched plugin data state:", fetchedPluginData);

    if (!pluginDataFromStorage) {
      window.toast(
        "Please fetch plugin data first by clicking the Fetch button",
        "warning"
      );
      return;
    }

    if (!pluginSlug.trim()) {
      window.toast("Please enter a plugin slug", "warning");
      return;
    }

    setIsAddingPlugin(true);
    try {
      const token = getValidToken();

      if (!token) {
        console.error("No valid authentication token found");
        autoLogout("No valid authentication token found. Please login again.");
        return;
      }

      // console.log(
      //   "🚀 Starting 2-step plugin addition workflow for plugin:",
      //   pluginSlug.trim()
      // );

      // Send plugin data from cookies to backend for the 2-step workflow
      const BASE_URL =
        import.meta.env.VITE_API_BASE_URL || "http://localhost:5001";
      const response = await fetch(`${BASE_URL}/api/plugins/added-with-data`, {
        method: "POST",
        headers: {
          Authorization: `Bearer ${token}`,
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          slug: pluginSlug.trim(),
          pluginData: pluginDataFromStorage,
        }),
      });

      if (!response.ok) {
        if (response.status === 401) {
          console.warn("Authentication failed during plugin addition");

          // Try to get the specific error message from the response
          let errorMessage = "Your session has expired. Please login again.";
          try {
            const errorData = await response.json();
            if (errorData.message) {
              errorMessage = errorData.message;
            }
          } catch (e) {
            // If we can't parse the error response, use default message
          }

          autoLogout(errorMessage);
          return;
        } else if (response.status === 413) {
          // Handle payload too large error
          console.warn("Payload too large error during plugin addition");
          try {
            const errorData = await response.json();
            window.toast(
              errorData.message ||
                "Plugin data is too large. Please try again or contact support.",
              "error"
            );
          } catch (e) {
            window.toast(
              "Plugin data is too large. Please try again or contact support.",
              "error"
            );
          }
          return;
        }
      }

      const data = await response.json();

      if (data.success) {
        window.toast(data.message, "success");

        // Clear plugin data from localStorage after successful addition
        clearPluginLocalStorage();

        // Reset form state
        setPluginSlug("");
        setIsModalOpen(false);
        setFetchedPluginData(null);

        // Reload added plugins to get the latest data
        await loadAddedPlugins();
      } else {
        window.toast(data.message || "Failed to add plugin", "error");
      }
    } catch (error) {
      console.error("Add plugin error:", error);

      // Handle JWT token expiration errors
      if (
        error.message &&
        (error.message.includes("expired") ||
          error.message.includes("token") ||
          error.message.includes("Authentication failed"))
      ) {
        console.warn(
          "JWT token error detected during plugin addition:",
          error.message
        );
        autoLogout(error.message);
        return;
      }

      window.toast("Failed to add plugin. Please try again.", "error");
    } finally {
      setIsAddingPlugin(false);
    }
  };

  const handleRemovePlugin = (pluginSlug) => {
    const plugin =
      addedPlugins.find((p) => p.slug === pluginSlug) ||
      addedPluginsListData.find((p) => p.slug === pluginSlug);
    setPluginToDelete(plugin);
    setShowDeleteModal(true);
  };

  const confirmDeletePlugin = async () => {
    if (!pluginToDelete) return;

    try {
      const token = getValidToken();

      if (!token) {
        console.error("No valid authentication token found");
        autoLogout("No valid authentication token found. Please login again.");
        return;
      }

      const BASE_URL =
        import.meta.env.VITE_API_BASE_URL || "http://localhost:5001";
      const response = await fetch(
        `${BASE_URL}/api/plugins/added/${pluginToDelete.slug}`,
        {
          method: "DELETE",
          headers: {
            Authorization: `Bearer ${token}`,
            "Content-Type": "application/json",
          },
        }
      );

      if (!response.ok && response.status === 401) {
        console.warn("Authentication failed during plugin removal");

        // Try to get the specific error message from the response
        let errorMessage = "Your session has expired. Please login again.";
        try {
          const errorData = await response.json();
          if (errorData.message) {
            errorMessage = errorData.message;
          }
        } catch (e) {
          // If we can't parse the error response, use default message
        }

        autoLogout(errorMessage);
        return;
      }

      const data = await response.json();
      if (data.success) {
        window.toast("Plugin removed successfully", "success");
        // Reload added plugins
        await loadAddedPlugins();
      } else {
        window.toast(data.message || "Failed to remove plugin", "error");
      }
    } catch (error) {
      console.error("Remove plugin error:", error);

      // Handle JWT token expiration errors
      if (
        error.message &&
        (error.message.includes("expired") ||
          error.message.includes("token") ||
          error.message.includes("Authentication failed"))
      ) {
        console.warn(
          "JWT token error detected during plugin removal:",
          error.message
        );
        autoLogout(error.message);
        return;
      }

      window.toast("Failed to remove plugin", "error");
    } finally {
      setShowDeleteModal(false);
      setPluginToDelete(null);
    }
  };

  // Refresh plugin data
  const handleRefreshPlugin = async (pluginSlug) => {
    try {
      const token = getValidToken();

      if (!token) {
        console.error("No valid authentication token found");
        autoLogout("No valid authentication token found. Please login again.");
        return;
      }

      const BASE_URL =
        import.meta.env.VITE_API_BASE_URL || "http://localhost:5001";
      const response = await fetch(
        `${BASE_URL}/api/plugins/added/${pluginSlug}/refresh`,
        {
          method: "POST",
          headers: {
            Authorization: `Bearer ${token}`,
            "Content-Type": "application/json",
          },
        }
      );

      if (!response.ok && response.status === 401) {
        console.warn("Authentication failed during plugin refresh");

        // Try to get the specific error message from the response
        let errorMessage = "Your session has expired. Please login again.";
        try {
          const errorData = await response.json();
          if (errorData.message) {
            errorMessage = errorData.message;
          }
        } catch (e) {
          // If we can't parse the error response, use default message
        }

        autoLogout(errorMessage);
        return;
      }

      const data = await response.json();
      if (data.success) {
        window.toast(data.message, "success");
        // Reload added plugins to show updated data
        await loadAddedPlugins();
      } else {
        window.toast(data.message || "Failed to refresh plugin", "error");
      }
    } catch (error) {
      console.error("Refresh plugin error:", error);

      // Handle JWT token expiration errors
      if (
        error.message &&
        (error.message.includes("expired") ||
          error.message.includes("token") ||
          error.message.includes("Authentication failed"))
      ) {
        console.warn(
          "JWT token error detected during plugin refresh:",
          error.message
        );
        autoLogout(error.message);
        return;
      }

      window.toast("Failed to refresh plugin", "error");
    }
  };

  // Refresh all plugins sequentially with 60-second delay
  const handleRefreshAllPlugins = async () => {
    try {
      setIsRefreshingAll(true);
      setRefreshProgress({ current: 0, total: 0, currentPlugin: null });

      const token = getValidToken();
      if (!token) {
        console.error("No valid authentication token found");
        autoLogout("No valid authentication token found. Please login again.");
        return;
      }

      const BASE_URL =
        import.meta.env.VITE_API_BASE_URL || "http://localhost:5001";

      // First, get all plugin slugs
      const slugsResponse = await fetch(`${BASE_URL}/api/plugins/added/slugs`, {
        headers: {
          Authorization: `Bearer ${token}`,
          "Content-Type": "application/json",
        },
      });

      if (!slugsResponse.ok) {
        if (slugsResponse.status === 401) {
          console.warn("Authentication failed during plugin slugs fetch");
          let errorMessage = "Your session has expired. Please login again.";
          try {
            const errorData = await slugsResponse.json();
            if (errorData.message) {
              errorMessage = errorData.message;
            }
          } catch (e) {
            // If we can't parse the error response, use default message
          }
          autoLogout(errorMessage);
          return;
        }
        throw new Error(
          `Failed to fetch plugin slugs: ${slugsResponse.status}`
        );
      }

      const slugsData = await slugsResponse.json();
      if (
        !slugsData.success ||
        !slugsData.slugs ||
        slugsData.slugs.length === 0
      ) {
        window.toast("No plugins found to refresh", "warning");
        return;
      }

      const pluginSlugs = slugsData.slugs;
      setRefreshProgress({
        current: 0,
        total: pluginSlugs.length,
        currentPlugin: null,
      });

      window.toast(
        `Starting refresh of ${pluginSlugs.length} plugins...`,
        "info"
      );

      // Refresh each plugin sequentially with 60-second delay
      for (let i = 0; i < pluginSlugs.length; i++) {
        const slug = pluginSlugs[i];

        setRefreshProgress({
          current: i + 1,
          total: pluginSlugs.length,
          currentPlugin: slug,
        });

        try {
          const refreshResponse = await fetch(
            `${BASE_URL}/api/plugins/added/${slug}/refresh`,
            {
              method: "POST",
              headers: {
                Authorization: `Bearer ${token}`,
                "Content-Type": "application/json",
              },
            }
          );

          if (!refreshResponse.ok && refreshResponse.status === 401) {
            console.warn("Authentication failed during plugin refresh");
            let errorMessage = "Your session has expired. Please login again.";
            try {
              const errorData = await refreshResponse.json();
              if (errorData.message) {
                errorMessage = errorData.message;
              }
            } catch (e) {
              // If we can't parse the error response, use default message
            }
            autoLogout(errorMessage);
            return;
          }

          const refreshData = await refreshResponse.json();
          if (refreshData.success) {
            console.log(`Successfully refreshed plugin: ${slug}`);
          } else {
            console.warn(
              `Failed to refresh plugin ${slug}: ${refreshData.message}`
            );
          }
        } catch (error) {
          console.error(`Error refreshing plugin ${slug}:`, error);
        }

        // Wait 60 seconds before the next refresh (except for the last plugin)
        if (i < pluginSlugs.length - 1) {
          await new Promise((resolve) => setTimeout(resolve, 60000));
        }
      }

      // Show success message and reload plugins
      window.toast("All plugins refreshed successfully!", "success");
      await loadAddedPlugins();
    } catch (error) {
      console.error("Refresh all plugins error:", error);

      // Handle JWT token expiration errors
      if (
        error.message &&
        (error.message.includes("expired") ||
          error.message.includes("token") ||
          error.message.includes("Authentication failed"))
      ) {
        console.warn(
          "JWT token error detected during refresh all:",
          error.message
        );
        autoLogout(error.message);
        return;
      }

      window.toast("Failed to refresh all plugins", "error");
    } finally {
      setIsRefreshingAll(false);
      setRefreshProgress(null);
    }
  };

  return (
    <div className="space-y-6">
      {/* Action Buttons */}
      <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-200">
        <div className="flex justify-between items-center">
          <div>
            <h2 className="text-2xl font-semibold text-gray-900">
              Welcome to Admin Dashboard
            </h2>
            <p className="text-sm text-gray-600">
              Manage your plugins, analyze keywords, and track performance all
              in one place.
            </p>
          </div>
          <div className="flex gap-4">
            {canAddPlugins && (
              <button
                onClick={handleFetchAllPlugins}
                disabled={isFetching}
                className={`flex items-center px-4 py-2 rounded-lg transition-colors ${
                  isFetching
                    ? "bg-gray-400 cursor-not-allowed"
                    : "bg-blue-600 hover:bg-blue-700"
                } text-white`}
                title="Fetch all 55,540+ plugins from WordPress repository"
              >
                {isFetching ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    Fetching All...
                  </>
                ) : (
                  <>
                    <Download className="h-4 w-4 mr-2" />
                    {hasPluginsInDB ? "Refetch" : "Fetch"}
                  </>
                )}
              </button>
            )}
            {canAddPlugins && (
              <button
                onClick={() => setIsModalOpen(true)}
                disabled={isFetching}
                className={`flex items-center px-4 py-2 rounded-lg transition-colors ${
                  isFetching
                    ? "bg-gray-400 cursor-not-allowed"
                    : "bg-green-600 hover:bg-green-700"
                } text-white`}
              >
                <Plus className="h-4 w-4 mr-2" />
                Add Plugin
              </button>
            )}
          </div>
        </div>

        {/* Enhanced Progress Indicator */}
        {fetchProgress && (
          <div
            className={`mt-4 p-4 rounded-lg border ${
              fetchProgress.error
                ? "bg-red-50 border-red-200"
                : "bg-blue-50 border-blue-200"
            }`}
          >
            <div className="flex items-center justify-between mb-2">
              <span
                className={`text-sm font-medium ${
                  fetchProgress.error ? "text-red-900" : "text-blue-900"
                }`}
              >
                {fetchProgress.message}
              </span>
              {fetchProgress.total > 0 && (
                <span
                  className={`text-sm ${
                    fetchProgress.error ? "text-red-700" : "text-blue-700"
                  }`}
                >
                  {fetchProgress.current?.toLocaleString()}/
                  {fetchProgress.total?.toLocaleString()}
                </span>
              )}
            </div>

            {/* Page Progress and Statistics */}
            {fetchProgress.page && fetchProgress.totalPages && (
              <div className="grid grid-cols-2 gap-4 mb-3 text-xs text-blue-700">
                <div className="space-y-1">
                  <div>
                    Page: {fetchProgress.page}/{fetchProgress.totalPages}
                  </div>
                  <div>Progress: {fetchProgress.percentComplete || 0}%</div>
                </div>
                <div className="space-y-1">
                  <div>
                    ✅ {fetchProgress.successCount?.toLocaleString()} success
                  </div>
                  {fetchProgress.errorCount > 0 && (
                    <div className="text-red-600">
                      ❌ {fetchProgress.errorCount?.toLocaleString()} errors
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* Performance Statistics */}
            {(fetchProgress.pluginsPerSecond > 0 ||
              fetchProgress.estimatedTimeRemaining) && (
              <div className="grid grid-cols-2 gap-4 mb-3 text-xs text-blue-600">
                {fetchProgress.pluginsPerSecond > 0 && (
                  <div>Speed: {fetchProgress.pluginsPerSecond} plugins/sec</div>
                )}
                {fetchProgress.estimatedTimeRemaining && (
                  <div>
                    ETA:{" "}
                    {Math.round(
                      fetchProgress.estimatedTimeRemaining / 1000 / 60
                    )}{" "}
                    min
                  </div>
                )}
                {fetchProgress.averageTimePerPage && (
                  <div>
                    Avg: {Math.round(fetchProgress.averageTimePerPage / 1000)}
                    s/page
                  </div>
                )}
                {fetchProgress.successRate && (
                  <div>Success Rate: {fetchProgress.successRate}%</div>
                )}
              </div>
            )}

            {/* Completion Statistics */}
            {fetchProgress.totalDuration && (
              <div className="mb-3 text-xs text-green-700 bg-green-50 p-2 rounded">
                <div className="grid grid-cols-2 gap-2">
                  <div>
                    Duration:{" "}
                    {Math.round(fetchProgress.totalDuration / 1000 / 60)}{" "}
                    minutes
                  </div>
                  <div>
                    Avg Speed: {fetchProgress.pluginsPerSecond} plugins/sec
                  </div>
                  <div>Success Rate: {fetchProgress.successRate}%</div>
                  <div>
                    Pages: {fetchProgress.successCount}/
                    {fetchProgress.totalPages}
                  </div>
                </div>
              </div>
            )}

            {/* Progress Bar */}
            {fetchProgress.total > 0 && !fetchProgress.error && (
              <div className="w-full bg-blue-200 rounded-full h-3">
                <div
                  className="bg-blue-600 h-3 rounded-full transition-all duration-300 flex items-center justify-center"
                  style={{
                    width: `${Math.max(
                      2,
                      fetchProgress.percentComplete ||
                        (fetchProgress.current / fetchProgress.total) * 100
                    )}%`,
                  }}
                >
                  <span className="text-xs text-white font-medium">
                    {fetchProgress.percentComplete ||
                      Math.round(
                        (fetchProgress.current / fetchProgress.total) * 100
                      )}
                    %
                  </span>
                </div>
              </div>
            )}
          </div>
        )}
      </div>

      {/* Added Plugins Section */}
      <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-200">
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-xl font-semibold text-gray-900">
            Added Plugins ({addedPlugins.length})
          </h3>
          <div className="text-sm text-gray-500">
            {addedPlugins.length > 0
              ? `Showing ${addedPlugins.length} plugins`
              : "No plugins added yet"}
          </div>
        </div>

        {addedPlugins.length > 0 ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {addedPlugins.map((plugin) => (
              <PluginCard
                key={plugin.slug}
                plugin={plugin}
                onRemove={handleRemovePlugin}
                onRefresh={handleRefreshPlugin}
                canAddPlugins={canAddPlugins}
              />
            ))}
          </div>
        ) : (
          <div className="text-center py-12">
            <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <Package className="h-8 w-8 text-gray-400" />
            </div>
            <h4 className="text-lg font-medium text-gray-900 mb-2">
              No plugins added yet
            </h4>
            <p className="text-gray-600">
              Start tracking your WordPress plugins by adding them to your
              dashboard using the "Add Plugin" button above.
            </p>
          </div>
        )}
      </div>

      {/* Add Plugin Modal */}
      <Modal
        isOpen={isModalOpen}
        onClose={() => {
          if (!isAddingPlugin) {
            setIsModalOpen(false);
            setFetchedPluginData(null);
            setPluginSlug("");
            clearPluginLocalStorage(); // Clear localStorage when modal is closed
          }
        }}
        title="Add New Plugin"
      >
        <div className="space-y-4">
          <div>
            <label
              htmlFor="pluginSlug"
              className="block text-sm font-medium text-gray-700 mb-2"
            >
              Plugin Slug
            </label>
            <div className="flex space-x-2">
              <input
                id="pluginSlug"
                type="text"
                value={pluginSlug}
                onChange={(e) => setPluginSlug(e.target.value)}
                className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="e.g., my-awesome-plugin"
              />
              <button
                onClick={handleFetchPluginData}
                disabled={fetchingPluginData || !pluginSlug.trim()}
                className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
              >
                {fetchingPluginData ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                    <span>Fetching...</span>
                  </>
                ) : (
                  <>
                    <Download className="h-4 w-4" />
                    <span>Fetch</span>
                  </>
                )}
              </button>
            </div>
          </div>

          {/* Display fetched plugin data */}
          {fetchedPluginData && (
            <div className="bg-green-50 border border-green-200 rounded-lg p-4 space-y-3">
              <h4 className="font-semibold text-green-900">
                Plugin Information (Fetched from WordPress API)
              </h4>
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="text-green-700">Name:</span>
                  <span className="ml-2 font-medium text-green-900">
                    {fetchedPluginData.name}
                  </span>
                </div>
                <div>
                  <span className="text-green-700">Version:</span>
                  <span className="ml-2 font-medium text-green-900">
                    {fetchedPluginData.version || "N/A"}
                  </span>
                </div>
                <div>
                  <span className="text-green-700">Author:</span>
                  {/* <span className="ml-2 font-medium text-green-900">
                    {fetchedPluginData.author || "N/A"}
                  </span> */}

                  <span className="ml-2 font-medium text-green-900">
                    {(() => {
                      const authorHtml = fetchedPluginData.author;
                      if (!authorHtml) return "N/A";

                      const match = authorHtml.match(/<a[^>]*>(.*?)<\/a>/);
                      return match ? match[1] : authorHtml;
                    })()}
                  </span>
                </div>
                <div>
                  <span className="text-green-700">Rating:</span>
                  <span className="ml-2 font-medium text-green-900">
                    {fetchedPluginData.rating
                      ? `${fetchedPluginData.rating}/100`
                      : "N/A"}
                  </span>
                </div>
                <div>
                  <span className="text-green-700">Active Installs:</span>
                  <span className="ml-2 font-medium text-green-900">
                    {fetchedPluginData.active_installs
                      ? fetchedPluginData.active_installs.toLocaleString()
                      : "N/A"}
                  </span>
                </div>
                <div>
                  <span className="text-green-700">Last Updated:</span>
                  <span className="ml-2 font-medium text-green-900">
                    {fetchedPluginData.last_updated || "N/A"}
                  </span>
                </div>
                {/* <div className="col-span-2">
                  <span className="text-green-700">Description:</span>
                  <span className="ml-2 font-medium text-green-900">
                    {fetchedPluginData.short_description || "N/A"}
                  </span>
                </div> */}
                <div className="col-span-2">
                  <span className="text-green-700">
                    WordPress Requirements:
                  </span>
                  <span className="ml-2 font-medium text-green-900">
                    WP {fetchedPluginData.requires || "N/A"} | Tested up to{" "}
                    {fetchedPluginData.tested || "N/A"} | PHP{" "}
                    {fetchedPluginData.requires_php || "N/A"}
                  </span>
                </div>
              </div>
            </div>
          )}

          {/* Show message if no plugin data is fetched */}
          {!fetchedPluginData && (
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <p className="text-blue-800 text-sm">
                <strong>Step 1:</strong> Enter a plugin slug and click "Fetch"
                to retrieve plugin information from WordPress API.
                <br />
                <strong>Step 2:</strong> Once plugin data is displayed, click
                "Add Plugin" to add it to your dashboard.
              </p>
            </div>
          )}

          <div className="flex justify-end space-x-3 pt-4">
            <button
              onClick={() => {
                setIsModalOpen(false);
                setFetchedPluginData(null);
                setPluginSlug("");
                clearPluginLocalStorage(); // Clear localStorage when canceling
              }}
              disabled={isAddingPlugin}
              className="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Cancel
            </button>
            <button
              onClick={handleAddPlugin}
              disabled={isAddingPlugin || !fetchedPluginData}
              className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
            >
              {isAddingPlugin ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                  <span>Adding...</span>
                </>
              ) : (
                <span>Add Plugin</span>
              )}
            </button>
          </div>
        </div>
      </Modal>

      {/* Delete Confirmation Modal */}
      <Modal
        isOpen={showDeleteModal}
        onClose={() => {
          setShowDeleteModal(false);
          setPluginToDelete(null);
        }}
        title="Confirm Delete"
      >
        <div className="space-y-4">
          <div className="flex items-center space-x-3">
            <div className="w-12 h-12 bg-red-100 rounded-full flex items-center justify-center">
              <Trash2 className="h-6 w-6 text-red-600" />
            </div>
            <div>
              <h4 className="text-lg font-semibold text-gray-900">
                Delete Plugin
              </h4>
              <p className="text-gray-600">
                Are you sure you want to remove "
                {pluginToDelete?.displayName || pluginToDelete?.name}" from your
                added plugins?
              </p>
            </div>
          </div>

          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3">
            <p className="text-sm text-yellow-800">
              <strong>Warning:</strong> This action cannot be undone. The plugin
              will be removed from your dashboard and you'll need to add it
              again if you want to track it.
            </p>
          </div>

          <div className="flex justify-end space-x-3 pt-4">
            <button
              onClick={() => {
                setShowDeleteModal(false);
                setPluginToDelete(null);
              }}
              className="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors"
            >
              Cancel
            </button>
            <button
              onClick={confirmDeletePlugin}
              className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors flex items-center space-x-2"
            >
              <Trash2 className="h-4 w-4" />
              <span>Delete Plugin</span>
            </button>
          </div>
        </div>
      </Modal>
    </div>
  );
};

export default Dashboard;
